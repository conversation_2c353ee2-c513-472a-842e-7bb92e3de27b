# users/models.py
from datetime import datetime, timedelta, timezone
from django.contrib.auth.models import AbstractUser
from django.db import models

class User(AbstractUser):
    """
    Modèle utilisateur personnalisé.
    """
    USER_TYPE_CHOICES = (
        ('standard', 'Standard'),
        ('premium', 'Premium'),
        ('admin', 'Admin'),
    )

    REGIME_CHOICES = (
        ('non', 'Aucun régime particulier'),
        ('végétarien', 'Végétarien'),
    )

    user_type = models.CharField(max_length=20, choices=USER_TYPE_CHOICES, default='standard')
    bio = models.TextField(max_length=150, default="")
    avatar = models.ImageField(upload_to='avatars/', blank=True, null=True)
    regime = models.CharField(max_length=20, choices=REGIME_CHOICES, default='non')
    # tu peux ajouter d'autres champs spécifiques aux users premium

    is_premium = models.BooleanField(default=False)
    premium_expiry = models.DateTimeField(blank=True, null=True)

    def __str__(self):
        return self.username
    
    def upgrade_to_premium(self, expiry_date):
        self.is_premium = True
        self.premium_expiry = expiry_date
        self.user_type = 'premium'
        self.save()

    def downgrade_from_premium(self):
        self.is_premium = False
        self.premium_expiry = None
        self.user_type = 'standard'
        self.save()

    def get_likes_counter(self):
        today = datetime.now().date()
        like_counter, created = UserLikeCounter.objects.get_or_create(user=self, date=today)
        
        return like_counter

    # def get_superlikes_counter(self):
    #     today = timezone.now().date()
    #     return self.superlike_counter.filter(date__lte=today, date__gte=today-timedelta(days=1)).first()


class UserLikeCounter(models.Model):
    user = models.OneToOneField(User, on_delete=models.CASCADE, related_name='like_counter')
    likes_remaining = models.IntegerField(default=10)
    date = models.DateField(auto_now_add=True)

    def get_likes_remaining(self):
        return self.likes_remaining
    
    def can_like(self):
        return self.likes_remaining > 0
    
    def remove_like(self):
        if self.likes_remaining > 0:
            self.likes_remaining -= 1
            self.save()
            return True
        return False

class UserSuperlikeCounter(models.Model):
    user = models.OneToOneField(User, on_delete=models.CASCADE, related_name='superlike_counter')
    superlikes_remaining = models.IntegerField(default=5)
    date = models.DateField(auto_now_add=True)

    def get_superlikes_remaining(self):
        return self.superlikes_remaining
    
    def remove_superlike(self):
        if self.superlikes_remaining > 0:
            self.superlikes_remaining -= 1
            self.save()
            return True
        return False