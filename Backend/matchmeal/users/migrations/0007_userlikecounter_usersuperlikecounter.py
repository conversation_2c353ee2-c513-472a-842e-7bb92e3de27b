# Generated by Django 5.1.3 on 2025-09-29 13:49

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('users', '0006_user_is_premium_user_premium_expiry'),
    ]

    operations = [
        migrations.CreateModel(
            name='UserLikeCounter',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('likes_remaining', models.IntegerField(default=10)),
                ('date', models.DateField(auto_now_add=True)),
                ('user', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='like_counter', to=settings.AUTH_USER_MODEL)),
            ],
        ),
        migrations.CreateModel(
            name='UserSuperlikeCounter',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('superlikes_remaining', models.IntegerField(default=5)),
                ('date', models.DateField(auto_now_add=True)),
                ('user', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='superlike_counter', to=settings.AUTH_USER_MODEL)),
            ],
        ),
    ]
