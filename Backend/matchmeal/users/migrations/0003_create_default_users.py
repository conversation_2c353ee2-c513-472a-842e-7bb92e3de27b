# users/migrations/0002_create_default_users.py
from django.db import migrations
from django.contrib.auth.hashers import make_password

def create_default_users(apps, schema_editor):
    User = apps.get_model('users', 'User')

    # Utilisateur standard
    User.objects.create(
        username='tym',
        email='<EMAIL>',
        password=make_password('tym'),
        user_type='admin',
        is_staff=True,
        is_superuser=True,
        is_active=True,
    )


class Migration(migrations.Migration):

    dependencies = [
        ('users', '0002_create_default_users'),  # ← mettez le nom de la migration initiale de users
    ]

    operations = [
        migrations.RunPython(create_default_users),
    ]
