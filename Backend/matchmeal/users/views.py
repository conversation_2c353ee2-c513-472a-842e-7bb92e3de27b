# users/views.py
from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework import status
from rest_framework.permissions import IsA<PERSON><PERSON>icated, AllowAny
from django.contrib.auth import authenticate
from rest_framework_simplejwt.tokens import RefreshToken
from .serializers import RegisterSerializer, UserSerializer, UserUpdateSerializer
from .permissions import IsPremiumUser

class RegisterView(APIView):
    permission_classes = [AllowAny]

    def post(self, request):
        serializer = RegisterSerializer(data=request.data)
        if serializer.is_valid():
            user = serializer.save()
            refresh = RefreshToken.for_user(user)
            return Response({
                "message": "Utilisateur créé avec succès",
                "access": str(refresh.access_token),
                "refresh": str(refresh)
            }, status=status.HTTP_201_CREATED)
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

class LoginView(APIView):
    permission_classes = [AllowAny]

    def post(self, request):
        username = request.data.get('username')
        password = request.data.get('password')
        user = authenticate(username=username, password=password)
        if user:
            refresh = RefreshToken.for_user(user)
            return Response({
                "message": "Connexion réussie",
                "access": str(refresh.access_token),
                "refresh": str(refresh)
            }, status=status.HTTP_200_OK)
        return Response({"error": "Nom d’utilisateur ou mot de passe incorrect"}, status=status.HTTP_401_UNAUTHORIZED)

class ProfileView(APIView):
    permission_classes = [IsAuthenticated]

    def get(self, request):
        serializer = UserSerializer(request.user)
        return Response(serializer.data, status=status.HTTP_200_OK)

    def patch(self, request):
        serializer = UserUpdateSerializer(request.user, data=request.data, partial=True)
        if serializer.is_valid():
            serializer.save()
            # Retourner les données complètes du profil mis à jour
            user_serializer = UserSerializer(request.user)
            return Response({
                "message": "Profil mis à jour avec succès",
                "user": user_serializer.data
            }, status=status.HTTP_200_OK)
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

class GetProfileView(APIView):
    permission_classes = [IsAuthenticated]

    def get(self, request):
        serializer = UserSerializer(request.user)

        matchs = request.user.matched_recipes.all().count()
        pays = request.user.matched_recipes.all().values_list('recipe__origin', flat=True).distinct().count()

        return Response({
            "user": serializer.data,
            "matchs": matchs,
            "pays": pays
        }, status=status.HTTP_200_OK)

class PremiumContentView(APIView):
    permission_classes = [IsAuthenticated, IsPremiumUser]

    def get(self, request):
        return Response({"message": "Contenu réservé aux utilisateurs premium"}, status=status.HTTP_200_OK)
