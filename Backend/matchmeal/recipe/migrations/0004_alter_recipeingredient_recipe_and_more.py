# Generated by Django 5.1.3 on 2025-09-27 19:34

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('recipe', '0003_rename_ingredient_ingredient_name_and_more'),
    ]

    operations = [
        migrations.AlterField(
            model_name='recipeingredient',
            name='recipe',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='ingredients', to='recipe.recipe'),
        ),
        migrations.AlterField(
            model_name='recipeinstruction',
            name='recipe',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='instructions', to='recipe.recipe'),
        ),
        migrations.AlterField(
            model_name='recipematched',
            name='recipe',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='matched', to='recipe.recipe'),
        ),
    ]
