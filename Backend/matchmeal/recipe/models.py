from django.db import models

# Create your models here.
class Recipe(models.Model):
    name = models.CharField(max_length=100)
    description = models.TextField()
    image = models.URLField(max_length=200)
    category = models.CharField(max_length=100)
    origin = models.CharField(max_length=100)
    rarety = models.CharField(max_length=100)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return self.name
    
class RecipeIngredient(models.Model):
    recipe = models.ForeignKey(Recipe, on_delete=models.CASCADE, related_name='ingredients')
    quantity = models.CharField(max_length=100)
    ingredient = models.ForeignKey('recipe.Ingredient', on_delete=models.CASCADE)

class Ingredient(models.Model):
    name = models.CharField(max_length=100)

class RecipeInstruction(models.Model):
    recipe = models.ForeignKey(Recipe, on_delete=models.CASCADE, related_name='instructions')
    step = models.IntegerField(default=1)
    instruction = models.TextField()





class RecipeUserInteraction(models.Model):
    user = models.ForeignKey('users.User', on_delete=models.CASCADE, related_name='recipe_interactions')
    recipe = models.ForeignKey(Recipe, on_delete=models.CASCADE, related_name='user_interactions')
    liked = models.BooleanField(default=False)
    disliked = models.BooleanField(default=False)
    interacted_at = models.DateTimeField(auto_now_add=True)
    matched = models.ForeignKey('RecipeMatched', on_delete=models.CASCADE, related_name='user_interactions', null=True, blank=True)

class RecipeMatched(models.Model):
    recipe = models.ForeignKey(Recipe, on_delete=models.CASCADE, related_name='matched')
    matched_at = models.DateTimeField(auto_now_add=True)
    user = models.ForeignKey('users.User', on_delete=models.CASCADE, related_name='matched_recipes')
