import React, { useState, useContext } from 'react';
import { View, TextInput, Button, Alert } from 'react-native';
import api from '../api/api';
import { AuthContext } from '../context/AuthContext';

const RegisterScreen = () => {
  const { login } = useContext(AuthContext);
  const [username, setUsername] = useState('');
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [password2, setPassword2] = useState('');

  const handleRegister = async () => {
    try {
      const res = await api.post('users/register/', { username, email, password, password2 });
      await login(res.data.access);
    } catch (err) {
      Alert.alert('Erreur', 'Impossible de créer le compte');
    }
  };

  return (
    <View style={{ padding: 20 }}>
      <TextInput placeholder="Username" value={username} onChangeText={setUsername} />
      <TextInput placeholder="Email" value={email} onChangeText={setEmail} />
      <TextInput placeholder="Password" value={password} onChangeText={setPassword} secureTextEntry />
      <TextInput placeholder="Confirm Password" value={password2} onChangeText={setPassword2} secureTextEntry />
      <Button title="Register" onPress={handleRegister} />
    </View>
  );
};

export default RegisterScreen;
