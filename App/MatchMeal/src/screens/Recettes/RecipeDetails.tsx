import React, { useState, useEffect } from 'react';
import { View, Text, StyleSheet, Image, ScrollView, TouchableOpacity } from 'react-native';
import { useNavigation } from '@react-navigation/native';
import api from '../../api/api';

// 🔹 Interface qui reflète exactement ce que ton API Django renvoie
interface Ingredient {
  name: string;
  quantite: string;
}

interface Recipe {
  id: number;
  nom: string;
  categorie: string;
  origine: string;
  image: string;
  rarety: string;
  ingredients: Ingredient[];
  instructions: string;
}

type RecipeDetailsProps = {
  route: {
    params: {
      recipe_id: number;
    };
  };
};

const RecipeDetails: React.FC<RecipeDetailsProps> = ({ route }) => {
  const { recipe_id } = route.params;
  const navigation = useNavigation();
  const [recipeDetails, setRecipeDetails] = useState<Recipe | null>(null);

  const fetchRecipeDetails = async () => {
    try {
      const response = await api.get(`api/get-recipe-details/${recipe_id}/`);
      setRecipeDetails(response.data);
    } catch (error) {
      console.error(error);
    }
  };

  useEffect(() => {
    fetchRecipeDetails();
  }, []);

  const handleBack = () => {
    navigation.goBack();
  };

  if (!recipeDetails) {
    return (
      <View style={styles.loadingContainer}>
        <Text>Chargement...</Text>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <ScrollView>
        <TouchableOpacity onPress={handleBack} style={styles.backButton}>
            <Text style={styles.backButtonText}>← Retour</Text>
        </TouchableOpacity>
        <View style={{ position: 'relative', alignItems: 'center' }}>

            <Image source={{ uri: recipeDetails.image }} style={styles.image} />
            <TouchableOpacity onPress={() => {}} style={styles.followButton}>
                <Text style={styles.backButtonText}>Cuisiner</Text>
            </TouchableOpacity>
        </View>

        <Text style={styles.title}>{recipeDetails.nom}</Text>
        <Text style={styles.meta}>
          Catégorie : {recipeDetails.categorie} | Origine : {recipeDetails.origine} | Rareté : {recipeDetails.rarety}
        </Text>

        <Text style={styles.sectionTitle}>Ingrédients</Text>
        {recipeDetails.ingredients.map((item, index) => (
          <Text key={index} style={styles.ingredient}>
            • {item.name} : {item.quantite}
          </Text>
        ))}

        <Text style={styles.sectionTitle}>Instructions</Text>
        <Text style={styles.instructions}>{recipeDetails.instructions}</Text>
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: { flex: 1, backgroundColor: '#fff' },
  loadingContainer: { flex: 1, justifyContent: 'center', alignItems: 'center' },
  backButton: {
    position: 'absolute',
    top: 80,
    left: 20,
    zIndex: 1,
    backgroundColor: 'rgba(255,255,255,0.7)',
    padding: 6,
    borderRadius: 6,
  },
  followButton: {
    bottom: 20,
    alignItems: 'center',
    zIndex: 1,
    backgroundColor: 'rgba(17, 255, 0, 1)',
    padding: 6,
    width: 100,
    marginHorizontal: 20,
    borderRadius: 6,
  },
  backButtonText: { fontSize: 16, fontWeight: 'bold', color: '#000' },
  image: { width: '100%', height: 300, resizeMode: 'cover' },
  title: { fontSize: 26, fontWeight: 'bold', margin: 20 },
  meta: { fontSize: 16, marginHorizontal: 20, marginBottom: 10, color: '#666' },
  sectionTitle: { fontSize: 20, fontWeight: 'bold', margin: 20, marginBottom: 10 },
  ingredient: { fontSize: 16, marginHorizontal: 30, marginBottom: 5 },
  instructions: { fontSize: 16, marginHorizontal: 20, marginBottom: 30, lineHeight: 22 },
});

export default RecipeDetails;
