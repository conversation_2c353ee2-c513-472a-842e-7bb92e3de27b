import React, { useState, useEffect } from "react";
import {
  View,
  Text,
  Image,
  TouchableOpacity,
  ScrollView,
  StyleSheet,
} from "react-native";
import { SafeAreaView } from "react-native-safe-area-context";
import { Ionicons } from "@expo/vector-icons";
import api from "../../api/api";

interface User {
    username: string;
    bio: string;
    // avatar: string;
    isPremium: boolean;
    stats: UserStats;
}

interface UserStats {
  recipesMatched: number;
  recipesViewed: number;
  countriesFound: number;
}

const ProfileScreen = ({ navigation }: any) => {
  const [user, setUser] = useState<User | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchProfile = async () => {
      try {
        const response = await api.get("users/get-profile/");
        console.log(response.data);
        setUser({
            username: response.data.user.username,
            bio: response.data.user.bio || "Aucune bio renseignée.",
            // avatar: response.data.user.avatar || "https://via.placeholder.com/150",
            isPremium: response.data.user.is_premium,
            stats: {
                recipesMatched: response.data.matchs || 0,
                recipesViewed: response.data.recipes_viewed || 0,
                countriesFound: response.data.pays || 0,
            }
        });
        setLoading(false);
      } catch (error) {
        console.error("Erreur lors de la récupération des données du profil:", error);
      }
    };

    fetchProfile();
  }, []);

    if (loading || !user) {
        return (
            <View style={[styles.container, { justifyContent: 'center', alignItems: 'center' }]}>
                <Text>Chargement du profil...</Text>
            </View>
        );
    }

  return (
    <SafeAreaView style={styles.container} edges={["top"]}>
      <ScrollView contentContainerStyle={{ padding: 20 }}>
        {/* Header */}
        <Text style={styles.header}>Mon profil</Text>

        {/* Avatar + Infos */}
        <View style={styles.profileSection}>
          {/* <Image source={{ uri: user.avatar }} style={styles.avatar} /> */}
          <Text style={styles.name}>
            {user.username}
          </Text>
          <Text style={styles.bio}>{user.bio}</Text>

          {/* Bouton Modifier le profil */}
          <TouchableOpacity
            style={styles.editProfileButton}
            onPress={() => navigation.navigate("VueProfil")}
          >
            <Ionicons name="pencil" size={18} color="#FF6B6B" />
            <Text style={styles.editProfileText}>Modifier le profil</Text>
          </TouchableOpacity>
        </View>

        {/* Bannière Premium */}
        <TouchableOpacity
          style={[styles.premiumBanner, user.isPremium ? styles.premiumActive : styles.premiumInactive]}
          onPress={() => navigation.navigate("Pricing")}
        >
          <View style={styles.premiumContent}>
            <Ionicons
              name={user.isPremium ? "diamond" : "diamond-outline"}
              size={24}
              color={user.isPremium ? "#FFD700" : "#666"}
            />
            <Text style={[styles.premiumText, user.isPremium ? styles.premiumTextActive : styles.premiumTextInactive]}>
              {user.isPremium ? "Membre Premium ✨" : "Passer au Premium"}
            </Text>
            <Ionicons name="chevron-forward" size={20} color="#666" />
          </View>
        </TouchableOpacity>

        {/* Statistiques principales */}
        <View style={styles.statsContainer}>
          <View style={styles.statCard}>
            <Ionicons name="heart" size={22} color="#FF6B6B" />
            <Text style={styles.statNumber}>{user.stats.recipesMatched}</Text>
            <Text style={styles.statLabel}>Matchés</Text>
          </View>

          <View style={styles.statCard}>
            <Ionicons name="eye" size={22} color="#4A90E2" />
            <Text style={styles.statNumber}>{user.stats.recipesViewed}</Text>
            <Text style={styles.statLabel}>Vues</Text>
          </View>

          <View style={styles.statCard}>
            <Ionicons name="globe-outline" size={22} color="#3B82F6" />
            <Text style={styles.statNumber}>{user.stats.countriesFound}</Text>
            <Text style={styles.statLabel}>Pays trouvés</Text>
          </View>
        </View>

        {/* Likes quotidiens */}
        <View style={styles.dailyLimitsContainer}>
          <Text style={styles.sectionTitle}>Aujourd'hui</Text>
          <View style={styles.limitsRow}>
            <View style={styles.limitCard}>
              <Ionicons name="heart" size={20} color="#FF6B6B" />
              <Text style={styles.limitNumber}>8</Text>
              <Text style={styles.limitLabel}>Likes restants</Text>
              <Text style={styles.limitTotal}>sur 10</Text>
            </View>

            <View style={styles.limitCard}>
              <Ionicons name="star" size={20} color="#FFD700" />
              <Text style={styles.limitNumber}>8</Text>
              <Text style={styles.limitLabel}>Superlikes</Text>
              <Text style={styles.limitTotal}>sur 10</Text>
            </View>
          </View>
        </View>

        {/* Options */}
        <View style={styles.options}>

          <TouchableOpacity
            style={styles.optionButton}
            onPress={() => navigation.navigate("Settings")}
          >
            <Ionicons name="settings-outline" size={22} color="#444" />
            <Text style={styles.optionText}>Paramètres</Text>
          </TouchableOpacity>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: { flex: 1, backgroundColor: "#fff" },
  header: { fontSize: 24, fontWeight: "700", color: "#222", marginBottom: 20 },
  profileSection: { alignItems: "center", marginBottom: 20 },
  avatar: {
    width: 120,
    height: 120,
    borderRadius: 60,
    borderWidth: 2,
    borderColor: "#FF6B6B",
  },
  name: { fontSize: 20, fontWeight: "700", marginTop: 12, color: "#333" },
  bio: {
    fontSize: 14,
    color: "#666",
    textAlign: "center",
    marginTop: 6,
    paddingHorizontal: 40,
  },
  editProfileButton: {
    flexDirection: "row",
    alignItems: "center",
    backgroundColor: "#fff",
    borderWidth: 1,
    borderColor: "#FF6B6B",
    borderRadius: 20,
    paddingHorizontal: 16,
    paddingVertical: 8,
    marginTop: 15,
    gap: 6,
  },
  editProfileText: {
    fontSize: 14,
    color: "#FF6B6B",
    fontWeight: "500",
  },

  // Bannière Premium
  premiumBanner: {
    marginBottom: 20,
    borderRadius: 12,
    padding: 16,
    shadowColor: "#000",
    shadowOpacity: 0.05,
    shadowRadius: 3,
    elevation: 2,
  },
  premiumActive: {
    backgroundColor: "#FFF8E1",
    borderWidth: 1,
    borderColor: "#FFD700",
  },
  premiumInactive: {
    backgroundColor: "#f9f9f9",
    borderWidth: 1,
    borderColor: "#eee",
  },
  premiumContent: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
  },
  premiumText: {
    flex: 1,
    marginLeft: 12,
    fontSize: 16,
    fontWeight: "600",
  },
  premiumTextActive: {
    color: "#E65100",
  },
  premiumTextInactive: {
    color: "#333",
  },

  // Statistiques
  statsContainer: {
    flexDirection: "row",
    justifyContent: "space-between",
    marginBottom: 20,
  },
  statCard: {
    flex: 1,
    alignItems: "center",
    backgroundColor: "#f9f9f9",
    paddingVertical: 16,
    marginHorizontal: 6,
    borderRadius: 12,
    shadowColor: "#000",
    shadowOpacity: 0.05,
    shadowRadius: 3,
    elevation: 2,
  },
  statNumber: { fontSize: 18, fontWeight: "700", color: "#222", marginTop: 6 },
  statLabel: { fontSize: 13, color: "#666", marginTop: 2 },

  // Section titre
  sectionTitle: {
    fontSize: 18,
    fontWeight: "600",
    color: "#333",
    marginBottom: 12,
  },

  // Limites quotidiennes
  dailyLimitsContainer: {
    marginBottom: 20,
  },
  limitsRow: {
    flexDirection: "row",
    justifyContent: "space-between",
  },
  limitCard: {
    flex: 1,
    alignItems: "center",
    backgroundColor: "#f9f9f9",
    paddingVertical: 16,
    marginHorizontal: 6,
    borderRadius: 12,
    shadowColor: "#000",
    shadowOpacity: 0.05,
    shadowRadius: 3,
    elevation: 2,
  },
  limitNumber: {
    fontSize: 20,
    fontWeight: "700",
    color: "#222",
    marginTop: 6
  },
  limitLabel: {
    fontSize: 12,
    color: "#666",
    marginTop: 2,
    textAlign: "center",
  },
  limitTotal: {
    fontSize: 11,
    color: "#999",
    marginTop: 1
  },

  // Options
  options: { marginTop: 10 },
  optionButton: {
    flexDirection: "row",
    alignItems: "center",
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: "#eee",
  },
  optionText: { fontSize: 16, marginLeft: 12, color: "#333", fontWeight: "500" },
});

export default ProfileScreen;
