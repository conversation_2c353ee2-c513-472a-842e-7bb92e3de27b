import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  TextInput,
  Image,
  Alert,
  Modal,
  ActivityIndicator,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Ionicons } from '@expo/vector-icons';
import * as ImagePicker from 'expo-image-picker';
import api from '../../api/api';

const VueProfilScreen = ({ navigation }: any) => {
  const [profileData, setProfileData] = useState({
    id: null as number | null,
    username: "",
    email: "",
    bio: "",
    avatar: "" as string | null,
    regime: "non",
  });

  const [isEditing, setIsEditing] = useState(false);
  const [showRegimeModal, setShowRegimeModal] = useState(false);
  const [tempProfileData, setTempProfileData] = useState({ ...profileData });
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);

  // Charger le profil utilisateur au montage du composant
  useEffect(() => {
    loadProfile();
  }, []);

  const loadProfile = async () => {
    try {
      setLoading(true);
      const response = await api.get('users/profile/');
      const userData = response.data;
      setProfileData(userData);
      setTempProfileData(userData);
    } catch (error) {
      console.error('Erreur lors du chargement du profil:', error);
      Alert.alert('Erreur', 'Impossible de charger le profil');
    } finally {
      setLoading(false);
    }
  };

  const regimeOptions = [
    { value: "non", label: "Aucun régime particulier", icon: "restaurant-outline" },
    { value: "végétarien", label: "Végétarien", icon: "leaf-outline" },
  ];

  const handleBack = () => {
    if (isEditing) {
      Alert.alert(
        "Modifications non sauvegardées",
        "Voulez-vous vraiment quitter sans sauvegarder ?",
        [
          { text: "Annuler", style: "cancel" },
          { text: "Quitter", onPress: () => navigation.goBack() },
        ]
      );
    } else {
      navigation.goBack();
    }
  };

  const handleEditToggle = () => {
    if (isEditing) {
      // Annuler les modifications
      setTempProfileData({ ...profileData });
      setIsEditing(false);
    } else {
      // Commencer l'édition
      setTempProfileData({ ...profileData });
      setIsEditing(true);
    }
  };

  const handleSave = async () => {
    try {
      setSaving(true);
      const updateData = {
        bio: tempProfileData.bio,
        regime: tempProfileData.regime,
        // Note: Pour l'avatar, il faudrait implémenter l'upload d'image
        // avatar: tempProfileData.avatar,
      };

      const response = await api.patch('users/profile/', updateData);
      setProfileData(response.data.user);
      setTempProfileData(response.data.user);
      setIsEditing(false);
      Alert.alert("Succès", "Profil mis à jour avec succès !");
    } catch (error) {
      console.error('Erreur lors de la sauvegarde:', error);
      Alert.alert("Erreur", "Impossible de sauvegarder les modifications");
    } finally {
      setSaving(false);
    }
  };

  const handleImagePicker = async () => {
    if (!isEditing) return;

    const permissionResult = await ImagePicker.requestMediaLibraryPermissionsAsync();
    
    if (permissionResult.granted === false) {
      Alert.alert("Permission requise", "L'accès à la galerie photo est nécessaire");
      return;
    }

    const result = await ImagePicker.launchImageLibraryAsync({
      mediaTypes: ['images'],
      allowsEditing: true,
      aspect: [1, 1],
      quality: 0.8,
    });

    if (!result.canceled) {
      setTempProfileData({
        ...tempProfileData,
        avatar: result.assets[0].uri,
      });
    }
  };

  const handleRegimeSelect = (regime: string) => {
    setTempProfileData({
      ...tempProfileData,
      regime,
    });
    setShowRegimeModal(false);
  };

  const getRegimeLabel = (regime: string) => {
    const option = regimeOptions.find(opt => opt.value === regime);
    return option ? option.label : "Non défini";
  };

  const getRegimeIcon = (regime: string) => {
    const option = regimeOptions.find(opt => opt.value === regime);
    return option ? option.icon : "help-outline";
  };

  if (loading) {
    return (
      <SafeAreaView style={styles.container} edges={["top"]}>
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color="#FF6B6B" />
          <Text style={styles.loadingText}>Chargement du profil...</Text>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container} edges={["top"]}>
      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity onPress={handleBack} style={styles.backButton}>
          <Ionicons name="arrow-back" size={24} color="#333" />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Mon Profil</Text>
        <TouchableOpacity onPress={handleEditToggle} style={styles.editButton}>
          <Ionicons 
            name={isEditing ? "close" : "pencil"} 
            size={24} 
            color={isEditing ? "#FF6B6B" : "#333"} 
          />
        </TouchableOpacity>
      </View>

      <ScrollView contentContainerStyle={styles.content}>
        {/* Photo de profil */}
        <View style={styles.avatarSection}>
          <TouchableOpacity 
            onPress={handleImagePicker}
            style={[styles.avatarContainer, isEditing && styles.avatarEditable]}
          >
            <Image
              source={{
                uri: (isEditing ? tempProfileData.avatar : profileData.avatar) || 'https://i.pravatar.cc/400'
              }}
              style={styles.avatar}
            />
            {isEditing && (
              <View style={styles.avatarOverlay}>
                <Ionicons name="camera" size={24} color="#fff" />
              </View>
            )}
          </TouchableOpacity>
          <Text style={styles.avatarHint}>
            {isEditing ? "Touchez pour changer la photo" : ""}
          </Text>
        </View>

        {/* Informations de base */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Informations personnelles</Text>
          
          <View style={styles.fieldContainer}>
            <Text style={styles.fieldLabel}>Nom d'utilisateur</Text>
            <Text style={styles.fieldValue}>{profileData.username}</Text>
            <Text style={styles.fieldHint}>Le nom d'utilisateur ne peut pas être modifié</Text>
          </View>
        </View>

        {/* Description */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Description</Text>
          
          <View style={styles.fieldContainer}>
            <Text style={styles.fieldLabel}>Bio</Text>
            {isEditing ? (
              <TextInput
                style={styles.textInput}
                value={tempProfileData.bio}
                onChangeText={(text) => setTempProfileData({...tempProfileData, bio: text})}
                placeholder="Décrivez-vous en quelques mots..."
                multiline
                numberOfLines={3}
                maxLength={150}
              />
            ) : (
              <Text style={styles.fieldValue}>{profileData.bio}</Text>
            )}
            <Text style={styles.fieldHint}>
              {isEditing ? `${tempProfileData.bio.length}/150 caractères` : ""}
            </Text>
          </View>
        </View>

        {/* Régime alimentaire */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Préférences alimentaires</Text>
          
          <TouchableOpacity 
            style={[styles.fieldContainer, styles.regimeField]}
            onPress={() => isEditing && setShowRegimeModal(true)}
            disabled={!isEditing}
          >
            <Text style={styles.fieldLabel}>Régime alimentaire</Text>
            <View style={styles.regimeValue}>
              <Ionicons 
                name={getRegimeIcon(isEditing ? tempProfileData.regime : profileData.regime)} 
                size={20} 
                color="#666" 
              />
              <Text style={styles.fieldValue}>
                {getRegimeLabel(isEditing ? tempProfileData.regime : profileData.regime)}
              </Text>
              {isEditing && (
                <Ionicons name="chevron-forward" size={20} color="#666" />
              )}
            </View>
          </TouchableOpacity>
        </View>

        {/* Bouton de sauvegarde */}
        {isEditing && (
          <TouchableOpacity
            style={[styles.saveButton, saving && styles.saveButtonDisabled]}
            onPress={handleSave}
            disabled={saving}
          >
            {saving ? (
              <ActivityIndicator size="small" color="#fff" />
            ) : (
              <Text style={styles.saveButtonText}>Sauvegarder les modifications</Text>
            )}
          </TouchableOpacity>
        )}
      </ScrollView>

      {/* Modal de sélection du régime */}
      <Modal
        visible={showRegimeModal}
        transparent={true}
        animationType="slide"
        onRequestClose={() => setShowRegimeModal(false)}
      >
        <View style={styles.modalOverlay}>
          <View style={styles.modalContent}>
            <Text style={styles.modalTitle}>Choisir votre régime alimentaire</Text>
            
            {regimeOptions.map((option) => (
              <TouchableOpacity
                key={option.value}
                style={[
                  styles.regimeOption,
                  tempProfileData.regime === option.value && styles.regimeOptionSelected
                ]}
                onPress={() => handleRegimeSelect(option.value)}
              >
                <Ionicons name={option.icon} size={24} color="#333" />
                <Text style={styles.regimeOptionText}>{option.label}</Text>
                {tempProfileData.regime === option.value && (
                  <Ionicons name="checkmark" size={24} color="#4CAF50" />
                )}
              </TouchableOpacity>
            ))}
            
            <TouchableOpacity 
              style={styles.modalCloseButton}
              onPress={() => setShowRegimeModal(false)}
            >
              <Text style={styles.modalCloseText}>Annuler</Text>
            </TouchableOpacity>
          </View>
        </View>
      </Modal>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#fff",
  },
  header: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    paddingHorizontal: 20,
    paddingVertical: 15,
    borderBottomWidth: 1,
    borderBottomColor: "#f0f0f0",
  },
  backButton: {
    padding: 5,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: "600",
    color: "#333",
  },
  editButton: {
    padding: 5,
  },
  content: {
    padding: 20,
  },
  avatarSection: {
    alignItems: "center",
    marginBottom: 30,
  },
  avatarContainer: {
    position: "relative",
  },
  avatarEditable: {
    opacity: 0.8,
  },
  avatar: {
    width: 120,
    height: 120,
    borderRadius: 60,
    borderWidth: 3,
    borderColor: "#FF6B6B",
  },
  avatarOverlay: {
    position: "absolute",
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: "rgba(0,0,0,0.5)",
    borderRadius: 60,
    justifyContent: "center",
    alignItems: "center",
  },
  avatarHint: {
    fontSize: 12,
    color: "#666",
    marginTop: 8,
    textAlign: "center",
  },
  section: {
    marginBottom: 25,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: "600",
    color: "#333",
    marginBottom: 15,
  },
  fieldContainer: {
    marginBottom: 15,
  },
  fieldLabel: {
    fontSize: 14,
    fontWeight: "500",
    color: "#666",
    marginBottom: 5,
  },
  fieldValue: {
    fontSize: 16,
    color: "#333",
    lineHeight: 22,
  },
  fieldHint: {
    fontSize: 12,
    color: "#999",
    marginTop: 5,
  },
  textInput: {
    borderWidth: 1,
    borderColor: "#ddd",
    borderRadius: 8,
    padding: 12,
    fontSize: 16,
    color: "#333",
    textAlignVertical: "top",
  },
  regimeField: {
    borderWidth: 1,
    borderColor: "#ddd",
    borderRadius: 8,
    padding: 15,
  },
  regimeValue: {
    flexDirection: "row",
    alignItems: "center",
    gap: 10,
  },
  saveButton: {
    backgroundColor: "#FF6B6B",
    borderRadius: 12,
    padding: 16,
    alignItems: "center",
    marginTop: 20,
  },
  saveButtonText: {
    color: "#fff",
    fontSize: 16,
    fontWeight: "600",
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: "rgba(0,0,0,0.5)",
    justifyContent: "flex-end",
  },
  modalContent: {
    backgroundColor: "#fff",
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    padding: 20,
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: "600",
    color: "#333",
    textAlign: "center",
    marginBottom: 20,
  },
  regimeOption: {
    flexDirection: "row",
    alignItems: "center",
    padding: 15,
    borderRadius: 8,
    marginBottom: 10,
    backgroundColor: "#f8f8f8",
    gap: 15,
  },
  regimeOptionSelected: {
    backgroundColor: "#e8f5e8",
    borderWidth: 1,
    borderColor: "#4CAF50",
  },
  regimeOptionText: {
    flex: 1,
    fontSize: 16,
    color: "#333",
  },
  modalCloseButton: {
    padding: 15,
    alignItems: "center",
    marginTop: 10,
  },
  modalCloseText: {
    fontSize: 16,
    color: "#666",
  },
  loadingContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
  },
  loadingText: {
    fontSize: 16,
    color: "#666",
    marginTop: 10,
  },
  saveButtonDisabled: {
    backgroundColor: "#ccc",
  },
});

export default VueProfilScreen;
