import React from 'react';
import { createNativeStackNavigator } from '@react-navigation/native-stack';
import ProfileScreen from '../screens/Profil/ProfileScreen';
import SettingsScreen from '../screens/Profil/SettingsScreen';
import PricingScreen from '../screens/Profil/PricingScreen';
import VueProfilScreen from '../screens/Profil/VueProfilScreen';

const Stack = createNativeStackNavigator();

const ProfileStack = () => {
  return (
    <Stack.Navigator
      screenOptions={{
        headerShown: false,
      }}
    >
      <Stack.Screen name="ProfileMain" component={ProfileScreen} options={{ title: 'Profil' }} />
      <Stack.Screen name="VueProfil" component={VueProfilScreen} options={{ title: 'Modifier le profil' }} />
      <Stack.Screen name="Settings" component={SettingsScreen} options={{ title: 'Options'}} />
      <Stack.Screen name="Pricing" component={PricingScreen} options={{ title: 'Abonnements'}} />
    </Stack.Navigator>
  );
};

export default ProfileStack;
