import React from 'react';
import { createNativeStackNavigator } from '@react-navigation/native-stack';
import ExplorerScreen from '../screens/ExplorerScreen';

const Stack = createNativeStackNavigator();

const ExplorerStack = () => {
  return (
    <Stack.Navigator
      screenOptions={{
        headerShown: false,
      }}
    >
      <Stack.Screen name="ExplorerMain" component={ExplorerScreen} options={{ title: 'Explorer' }} />
    </Stack.Navigator>
  );
};

export default ExplorerStack;
