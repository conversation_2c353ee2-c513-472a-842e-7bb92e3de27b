import React from 'react';
import { createNativeStackNavigator } from '@react-navigation/native-stack';
import SavedRecipesScreen from '../screens/Recettes/SavedRecipesScreen';
import RecipeDetails from '../screens/Recettes/RecipeDetails';

const Stack = createNativeStackNavigator();

const FavoritesStack = () => {
  return (
    <Stack.Navigator
      screenOptions={{
        headerShown: false,
      }}
    >
      <Stack.Screen name="SavedRecipes" component={SavedRecipesScreen} options={{ title: 'Favoris' }} />
      <Stack.Screen name="RecipeDetails" component={RecipeDetails} options={{ title: 'Détails' }} />
    </Stack.Navigator>
  );
};

export default FavoritesStack;
