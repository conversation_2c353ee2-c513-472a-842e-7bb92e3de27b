import React from 'react';
import { createBottomTabNavigator } from '@react-navigation/bottom-tabs';
import ExplorerStack from './ExplorerStack';
import FavoritesStack from './FavoritesStack';
import ProfileStack from './ProfileStack';
import { Ionicons } from '@expo/vector-icons';

const Tab = createBottomTabNavigator();

const TabNavigator = () => {
  return (
    <Tab.Navigator
      screenOptions={({ route }) => ({
        tabBarIcon: ({ color, size }) => {
          let iconName: any;
          if (route.name === 'Explorer') iconName = 'search';
          else if (route.name === 'Favoris') iconName = 'heart';
          else if (route.name === 'Profil') iconName = 'person';
          return <Ionicons name={iconName} size={size} color={color} />;
        },
        tabBarActiveTintColor: 'tomato',
        tabBarInactiveTintColor: 'gray',
      })}
    >
      <Tab.Screen name="Explorer" component={ExplorerStack} options={{ headerShown: false }} />
      <Tab.Screen name="Favoris" component={FavoritesStack} options={{ headerShown: false }} />
      <Tab.Screen name="Profil" component={ProfileStack} options={{ headerShown: false }} />
    </Tab.Navigator>
  );
};

export default TabNavigator;
