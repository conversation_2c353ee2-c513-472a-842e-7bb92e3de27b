import React, { useContext, useEffect, useState } from 'react';
import { NavigationContainer } from '@react-navigation/native';
import { createNativeStackNavigator } from '@react-navigation/native-stack';
import { AuthContext } from '../context/AuthContext';
import LoginScreen from '../screens/Auth/LoginScreen';
import RegisterScreen from '../screens/Auth/RegisterScreen';
import TabNavigator from './TabNavigator';
import AsyncStorage from '@react-native-async-storage/async-storage';
import OnboardingScreen from '../screens/Auth/OnboardingScreen';

const Stack = createNativeStackNavigator();

const AppNavigator = () => {
  const { isLoggedIn } = useContext(AuthContext);
    const [hasSeenOnboarding, setHasSeenOnboarding] = useState(false);


  useEffect(() => {
    const checkOnboarding = async () => {
      const value = await AsyncStorage.getItem('hasSeenOnboarding2');
      setHasSeenOnboarding(value === 'true');
    };
    checkOnboarding();
  }, []);

  return (
    <NavigationContainer>
      <Stack.Navigator>
        {isLoggedIn ? (
            !hasSeenOnboarding ? (
                <Stack.Screen name="Onboarding" options={{ headerShown: false }}>
                    {(props) => (
                    <OnboardingScreen 
                        {...props} 
                        setHasSeenOnboarding={setHasSeenOnboarding} 
                    />
                )}              
                </Stack.Screen>
            ) : (
                <Stack.Screen name="Main" component={TabNavigator} options={{ headerShown: false }} />
            )
        ) : (
          <>
            <Stack.Screen name="Login" component={LoginScreen} options={{ headerShown: false }} />
            <Stack.Screen name="Register" component={RegisterScreen} options={{ headerShown: false }} />
          </>
        )}
      </Stack.Navigator>
    </NavigationContainer>
  );
};

export default AppNavigator;
