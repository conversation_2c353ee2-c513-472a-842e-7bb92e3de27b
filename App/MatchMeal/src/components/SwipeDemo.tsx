import React, { useState, useRef } from "react";
import {
  View,
  Text,
  StyleSheet,
  Dimensions,
  TouchableOpacity,
  Alert,
} from "react-native";
import SwipeRecipes from "./SwipeRecipe";

const { width, height } = Dimensions.get("window");

// Données de démonstration
const demoRecipes = [
  {
    id: "1",
    nom: "Pasta Carbonara",
    categorie: "Italien",
    origine: "Italie",
    instructions: "Délicieuses pâtes à la carbonara...",
    image: "https://images.unsplash.com/photo-1621996346565-e3dbc353d2e5?w=400",
    rarety: "commun",
    ingredients: [
      { name: "<PERSON><PERSON><PERSON>", quantity: "400g" },
      { name: "Lardon<PERSON>", quantity: "200g" },
      { name: "Œufs", quantity: "3" },
      { name: "Parmesan", quantity: "100g" },
    ],
  },
  {
    id: "2",
    nom: "Sushi Saumon",
    categorie: "Japonais",
    origine: "Japon",
    instructions: "Sushi frais au saumon...",
    image: "https://images.unsplash.com/photo-1579584425555-c3ce17fd4351?w=400",
    rarety: "rare",
    ingredients: [
      { name: "Riz", quantity: "300g" },
      { name: "Saumon", quantity: "200g" },
      { name: "Nori", quantity: "5 feuilles" },
      { name: "Wasabi", quantity: "1 cuillère" },
    ],
  },
  {
    id: "3",
    nom: "Burger Truffe",
    categorie: "Américain",
    origine: "USA",
    instructions: "Burger gourmet à la truffe...",
    image: "https://images.unsplash.com/photo-1568901346375-23c9450c58cd?w=400",
    rarety: "legendaire",
    ingredients: [
      { name: "Pain brioche", quantity: "1" },
      { name: "Bœuf", quantity: "200g" },
      { name: "Truffe", quantity: "10g" },
      { name: "Roquette", quantity: "50g" },
    ],
  },
  {
    id: "4",
    nom: "Paella Royale",
    categorie: "Espagnol",
    origine: "Espagne",
    instructions: "Paella aux fruits de mer...",
    image: "https://images.unsplash.com/photo-1534080564583-6be75777b70a?w=400",
    rarety: "unique",
    ingredients: [
      { name: "Riz bomba", quantity: "400g" },
      { name: "Crevettes", quantity: "300g" },
      { name: "Moules", quantity: "500g" },
      { name: "Safran", quantity: "1g" },
    ],
  },
];

const SwipeDemo = () => {
  const [showDemo, setShowDemo] = useState(false);

  const handleStartDemo = () => {
    setShowDemo(true);
  };

  const handleBackToMenu = () => {
    setShowDemo(false);
  };

  if (showDemo) {
    return (
      <View style={styles.container}>
        <TouchableOpacity style={styles.backButton} onPress={handleBackToMenu}>
          <Text style={styles.backButtonText}>← Retour</Text>
        </TouchableOpacity>
        <SwipeRecipes />
      </View>
    );
  }

  return (
    <View style={styles.menuContainer}>
      <Text style={styles.title}>🎉 Swiper Amélioré</Text>
      <Text style={styles.subtitle}>Nouvelles fonctionnalités :</Text>
      
      <View style={styles.featuresList}>
        <Text style={styles.feature}>✨ Boutons d'action interactifs</Text>
        <Text style={styles.feature}>🎨 Animations de swipe améliorées</Text>
        <Text style={styles.feature}>🎊 Confettis lors des matchs</Text>
        <Text style={styles.feature}>📱 Feedback haptique différencié</Text>
        <Text style={styles.feature}>💖 Compteur de likes visible</Text>
        <Text style={styles.feature}>🔄 Animation de match redesignée</Text>
      </View>

      <TouchableOpacity style={styles.demoButton} onPress={handleStartDemo}>
        <Text style={styles.demoButtonText}>Tester le Swiper</Text>
      </TouchableOpacity>

      <View style={styles.instructions}>
        <Text style={styles.instructionsTitle}>Comment tester :</Text>
        <Text style={styles.instructionText}>• Swipe gauche/droite sur les cartes</Text>
        <Text style={styles.instructionText}>• Utilise les boutons en bas</Text>
        <Text style={styles.instructionText}>• Observe les animations colorées</Text>
        <Text style={styles.instructionText}>• Ressens le feedback haptique</Text>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#f5f5f5",
  },
  menuContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    backgroundColor: "#f5f5f5",
    padding: 20,
  },
  title: {
    fontSize: 32,
    fontWeight: "900",
    color: "#333",
    marginBottom: 10,
    textAlign: "center",
  },
  subtitle: {
    fontSize: 18,
    fontWeight: "600",
    color: "#666",
    marginBottom: 20,
  },
  featuresList: {
    marginBottom: 30,
  },
  feature: {
    fontSize: 16,
    color: "#555",
    marginBottom: 8,
    textAlign: "left",
  },
  demoButton: {
    backgroundColor: "#007AFF",
    paddingHorizontal: 40,
    paddingVertical: 15,
    borderRadius: 25,
    marginBottom: 30,
    shadowColor: "#000",
    shadowOpacity: 0.2,
    shadowRadius: 8,
    elevation: 5,
  },
  demoButtonText: {
    color: "#fff",
    fontSize: 18,
    fontWeight: "700",
  },
  instructions: {
    backgroundColor: "#fff",
    padding: 20,
    borderRadius: 15,
    shadowColor: "#000",
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  instructionsTitle: {
    fontSize: 16,
    fontWeight: "700",
    color: "#333",
    marginBottom: 10,
  },
  instructionText: {
    fontSize: 14,
    color: "#666",
    marginBottom: 5,
  },
  backButton: {
    position: "absolute",
    top: 60,
    left: 20,
    zIndex: 1000,
    backgroundColor: "rgba(255,255,255,0.9)",
    paddingHorizontal: 15,
    paddingVertical: 8,
    borderRadius: 20,
  },
  backButtonText: {
    fontSize: 16,
    fontWeight: "600",
    color: "#007AFF",
  },
});

export default SwipeDemo;
