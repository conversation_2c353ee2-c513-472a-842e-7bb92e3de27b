import axios from 'axios';
import AsyncStorage from '@react-native-async-storage/async-storage';

const API_URL = 'http://192.168.1.32:8000/'; // ton backend Django

const api = axios.create({
  baseURL: API_URL,
});

// Intercepteur : ajoute le token aux requêtes
api.interceptors.request.use(async config => {
  if (!config.url?.includes('login') && !config.url?.includes('register')) {
    const token = await AsyncStorage.getItem('accessToken');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
  }
  config.headers['Content-Type'] = 'application/json';
  return config;
});

// Intercepteur : gère les erreurs 401 (token expiré)
api.interceptors.response.use(
  response => response,
  async error => {
    const originalRequest = error.config;

    // Vérifie si c'est une erreur 401 et qu'on n'a pas déjà essayé de refresh
    if (error.response?.status === 401 && !originalRequest._retry) {
      originalRequest._retry = true;

      try {
        // Récupère le refresh token
        const refreshToken = await AsyncStorage.getItem('refreshToken');
        if (!refreshToken) {
          throw new Error("Pas de refresh token trouvé");
        }

        // Demande un nouveau access token au backend
        const res = await axios.post(`${API_URL}api/token/refresh/`, {
          refresh: refreshToken,
        });

        const newAccessToken = res.data.access;

        // Sauvegarde le nouveau token
        await AsyncStorage.setItem('accessToken', newAccessToken);

        // Mets à jour l'entête Authorization
        api.defaults.headers.common['Authorization'] = `Bearer ${newAccessToken}`;
        originalRequest.headers['Authorization'] = `Bearer ${newAccessToken}`;

        // Relance la requête initiale
        return api(originalRequest);
      } catch (err) {
        console.error("Erreur lors du refresh token :", err);
        // Tu peux ici rediriger vers la page login si refresh aussi expiré
        return Promise.reject(error);
      }
    }

    return Promise.reject(error);
  }
);

export default api;