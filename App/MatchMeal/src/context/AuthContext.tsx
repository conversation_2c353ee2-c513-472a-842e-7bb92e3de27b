import React, { createContext, useState, ReactNode, useEffect } from 'react';
import AsyncStorage from '@react-native-async-storage/async-storage';
import axios from 'axios';

const API_URL = 'http://192.168.1.32:8000/'; // ton backend Django

interface AuthContextData {
  isLoggedIn: boolean;
  login: (accessToken: string, refreshToken: string) => Promise<void>;
  logout: () => Promise<void>;
  refreshAccessToken: () => Promise<string | null>;
}

export const AuthContext = createContext<AuthContextData>({
  isLoggedIn: false,
  login: async () => {},
  logout: async () => {},
  refreshAccessToken: async () => null,
});

export const AuthProvider = ({ children }: { children: ReactNode }) => {
  const [isLoggedIn, setIsLoggedIn] = useState(false);

  // Vérifie si on a déjà un token en mémoire (persistant après redémarrage app)
  useEffect(() => {
    const checkLoginStatus = async () => {
      const refresh = await AsyncStorage.getItem('refreshToken');
      if (refresh) {
        setIsLoggedIn(true);
      }
    };
    checkLoginStatus();
  }, []);

  // Login : stocke access + refresh
  const login = async (accessToken: string, refreshToken: string) => {
    await AsyncStorage.setItem('accessToken', accessToken);
    await AsyncStorage.setItem('refreshToken', refreshToken);
    setIsLoggedIn(true);
  };

  // Logout : supprime access + refresh
  const logout = async () => {
    await AsyncStorage.removeItem('accessToken');
    await AsyncStorage.removeItem('refreshToken');
    setIsLoggedIn(false);
  };

  // Utilise le refresh token pour obtenir un nouvel access token
  const refreshAccessToken = async (): Promise<string | null> => {
    try {
      const refreshToken = await AsyncStorage.getItem('refreshToken');
      if (!refreshToken) return null;

      const response = await axios.post(`${API_URL}api/token/refresh/`, {
        refresh: refreshToken,
      });

      const newAccessToken = response.data.access;
      await AsyncStorage.setItem('accessToken', newAccessToken);

      return newAccessToken;
    } catch (error) {
      console.error("Erreur lors du refresh token :", error);
      await logout(); // si refresh expiré → déconnexion
      return null;
    }
  };

  return (
    <AuthContext.Provider value={{ isLoggedIn, login, logout, refreshAccessToken }}>
      {children}
    </AuthContext.Provider>
  );
};