{"name": "matchmeal", "version": "1.0.0", "main": "index.ts", "scripts": {"start": "expo start", "android": "expo start --android", "ios": "expo start --ios", "web": "expo start --web"}, "dependencies": {"@react-native-async-storage/async-storage": "^2.2.0", "@react-navigation/bottom-tabs": "^7.4.7", "@react-navigation/native": "^7.1.17", "@react-navigation/native-stack": "^7.3.26", "axios": "^1.12.2", "expo": "~54.0.10", "expo-av": "^16.0.7", "expo-blur": "~15.0.7", "expo-haptics": "~15.0.7", "expo-image-picker": "^17.0.8", "expo-status-bar": "~3.0.8", "react": "19.1.0", "react-native": "0.81.4", "react-native-deck-swiper": "^2.0.19", "react-native-gesture-handler": "~2.28.0", "react-native-haptic-feedback": "^2.3.3", "react-native-reanimated": "~4.1.0", "react-native-safe-area-context": "~5.6.0", "react-native-screens": "~4.16.0", "react-native-svg": "15.12.1"}, "devDependencies": {"@types/react": "~19.1.0", "typescript": "~5.9.2"}, "private": true}